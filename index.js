// PostgreSQL connection test application
const { Client } = require('pg');
const dbConfig = require('./config');

// Create a new PostgreSQL client
const client = new Client(dbConfig);

// Function to test the database connection
async function testConnection() {
  try {
    console.log('Attempting to connect to PostgreSQL database...');
    console.log(`Host: ${dbConfig.host}`);
    console.log(`Database: ${dbConfig.database}`);
    console.log(`User: ${dbConfig.user}`);
    
    // Connect to the database
    await client.connect();
    
    // Execute a simple query to verify connection
    const result = await client.query('SELECT NOW() as current_time');
    
    console.log('\n✅ Connection successful!');
    console.log(`Server time: ${result.rows[0].current_time}`);
    
    // Close the connection
    await client.end();
    console.log('Connection closed.');
    
  } catch (error) {
    console.error('\n❌ Connection failed:');
    console.error(error.message);
    
    // Try to close the connection if it was opened
    try {
      await client.end();
    } catch (closeError) {
      // Ignore errors when closing an already failed connection
    }
  }
}

// Run the connection test
testConnection();
