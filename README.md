# PostgreSQL Connection Test

A simple Node.js console application that tests the connection to a PostgreSQL database.

## Prerequisites

- Node.js installed
- PostgreSQL server running and accessible

## Configuration

Edit the `config.js` file to set your PostgreSQL connection parameters:

```js
module.exports = {
  host: 'localhost',      // PostgreSQL server hostname
  port: 5432,             // PostgreSQL server port
  database: 'postgres',   // Database name
  user: 'postgres',       // Database user
  password: 'postgres',   // Database password
  connectionTimeoutMillis: 5000,
};
```

## Running the Application

```bash
node index.js
```

The application will attempt to connect to the PostgreSQL database using the provided configuration and report whether the connection was successful.
