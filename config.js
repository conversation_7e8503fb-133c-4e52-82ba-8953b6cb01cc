// Database connection configuration
module.exports = {
  // Option 1: Connection string (preferred)
  // Format: postgres://user:password@host:port/database
  connectionString: 'postgres://postgres:postgres@localhost:5432/postgres',

  // Option 2: Individual connection parameters (used if connectionString is not provided)
  host: 'localhost',
  port: 5432,
  database: 'postgres',
  user: 'postgres',
  password: 'postgres',

  // Connection timeout in milliseconds
  connectionTimeoutMillis: 5000,
};
