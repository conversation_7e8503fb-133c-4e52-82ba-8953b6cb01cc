// Database connection configuration
module.exports = {
  // Option 1: Connection string (preferred)
  // Format: postgres://user:password@host:port/database
  connectionString: 'postgresql://chris:rhbJCl{::Y.x<l.u?\\<EMAIL>/flexibleserverdb?sslmode=require',

  // SSL configuration to handle self-signed certificates
  ssl: {
    // rejectUnauthorized: false // This allows self-signed certificates
  },
  // Connection timeout in milliseconds
  connectionTimeoutMillis: 5000,
};
